<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dify 对话应用测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            text-align: center;
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 100%;
        }

        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .avatar-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .avatar-item {
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-radius: 15px;
            overflow: hidden;
            background: #f8f9fa;
            padding: 20px;
        }

        .avatar-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }

        .avatar-item img {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 10px;
        }

        .avatar-item h3 {
            color: #333;
            font-size: 1.2em;
            margin-bottom: 5px;
        }

        .avatar-item p {
            color: #666;
            font-size: 0.9em;
        }

        /* 对话框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            height: 80vh;
            display: flex;
            flex-direction: column;
            box-shadow: 0 25px 50px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 1.5em;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s ease;
        }

        .close:hover {
            opacity: 0.7;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message.assistant {
            justify-content: flex-start;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }

        .message.user .message-content {
            background: #007bff;
            color: white;
            border-bottom-right-radius: 5px;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 5px;
        }

        .chat-input {
            padding: 20px;
            border-top: 1px solid #e0e0e0;
            background: white;
            border-radius: 0 0 15px 15px;
        }

        .input-group {
            display: flex;
            gap: 10px;
        }

        .input-group input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            outline: none;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .input-group input:focus {
            border-color: #007bff;
        }

        .input-group button {
            padding: 12px 24px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s ease;
        }

        .input-group button:hover:not(:disabled) {
            background: #0056b3;
        }

        .input-group button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 10px;
            color: #666;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI 角色对话</h1>
        <p style="margin-bottom: 30px; color: #666; font-size: 1.1em;">点击下方角色头像开始对话</p>
        
        <div class="avatar-grid">
            <div class="avatar-item" onclick="openChat('爱因斯坦')">
                <img src="https://via.placeholder.com/100x100/4a90e2/ffffff?text=E" alt="爱因斯坦">
                <h3>爱因斯坦</h3>
                <p>物理学家</p>
            </div>
            <div class="avatar-item" onclick="openChat('莎士比亚')">
                <img src="https://via.placeholder.com/100x100/7b68ee/ffffff?text=S" alt="莎士比亚">
                <h3>莎士比亚</h3>
                <p>文学家</p>
            </div>
            <div class="avatar-item" onclick="openChat('孔子')">
                <img src="https://via.placeholder.com/100x100/32cd32/ffffff?text=孔" alt="孔子">
                <h3>孔子</h3>
                <p>思想家</p>
            </div>
            <div class="avatar-item" onclick="openChat('居里夫人')">
                <img src="https://via.placeholder.com/100x100/ff69b4/ffffff?text=C" alt="居里夫人">
                <h3>居里夫人</h3>
                <p>科学家</p>
            </div>
        </div>
    </div>

    <!-- 对话框模态窗口 -->
    <div id="chatModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="chatTitle">与 AI 对话</h2>
                <span class="close" onclick="closeChat()">&times;</span>
            </div>
            <div class="chat-container">
                <div class="chat-messages" id="chatMessages">
                    <div class="message assistant">
                        <div class="message-content">
                            你好！我是你选择的角色，有什么想聊的吗？
                        </div>
                    </div>
                </div>
                <div class="loading" id="loading">正在思考中...</div>
                <div class="chat-input">
                    <div class="input-group">
                        <input type="text" id="messageInput" placeholder="输入你的问题..." onkeypress="handleKeyPress(event)">
                        <button onclick="sendMessage()" id="sendButton">发送</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 配置信息 - 请替换为你的实际配置
        const CONFIG = {
            API_URL: 'https://api.dify.ai/v1/chat-messages',
            API_KEY: 'YOUR_API_KEY_HERE', // 请替换为你的 Dify API Key
        };

        // 使用提示
        console.log('🚀 使用说明：');
        console.log('1. 请将 CONFIG.API_KEY 替换为你的 Dify API Key');
        console.log('2. 确保你的 Dify 应用已配置 people 变量');
        console.log('3. 点击角色头像开始对话！');

        let currentPeople = '';
        let conversationId = '';

        function openChat(people) {
            currentPeople = people;
            conversationId = ''; // 重置会话ID
            document.getElementById('chatTitle').textContent = `与 ${people} 对话`;
            document.getElementById('chatModal').style.display = 'block';
            
            // 清空聊天记录
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = `
                <div class="message assistant">
                    <div class="message-content">
                        你好！我是${people}，有什么想聊的吗？
                    </div>
                </div>
            `;
            
            // 聚焦输入框
            document.getElementById('messageInput').focus();
        }

        function closeChat() {
            document.getElementById('chatModal').style.display = 'none';
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        async function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            
            if (!message) return;
            
            // 检查API配置
            if (CONFIG.API_KEY === 'YOUR_API_KEY_HERE') {
                showError('请先配置你的 API Key');
                return;
            }

            // 添加用户消息到聊天界面
            addMessage('user', message);
            messageInput.value = '';
            
            // 显示加载状态
            showLoading(true);
            setSendButtonState(false);

            try {
                const response = await fetch(CONFIG.API_URL, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${CONFIG.API_KEY}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        inputs: {
                            people: currentPeople
                        },
                        query: message,
                        response_mode: 'blocking',
                        conversation_id: conversationId,
                        user: 'web-user-' + Date.now(),
                        auto_generate_name: true
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                
                // 更新会话ID
                if (data.conversation_id) {
                    conversationId = data.conversation_id;
                }
                
                // 添加AI回复到聊天界面
                addMessage('assistant', data.answer);
                
            } catch (error) {
                console.error('Error:', error);
                showError('发送消息失败，请检查网络连接和API配置');
            } finally {
                showLoading(false);
                setSendButtonState(true);
            }
        }

        function addMessage(sender, content) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            messageDiv.innerHTML = `
                <div class="message-content">
                    ${content}
                </div>
            `;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function showLoading(show) {
            const loading = document.getElementById('loading');
            loading.style.display = show ? 'block' : 'none';
        }

        function setSendButtonState(enabled) {
            const sendButton = document.getElementById('sendButton');
            sendButton.disabled = !enabled;
            sendButton.textContent = enabled ? '发送' : '发送中...';
        }

        function showError(message) {
            const chatMessages = document.getElementById('chatMessages');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            chatMessages.appendChild(errorDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 点击模态窗口外部关闭对话框
        window.onclick = function(event) {
            const modal = document.getElementById('chatModal');
            if (event.target === modal) {
                closeChat();
            }
        }
    </script>
</body>
</html>
